# Yuan项目通讯架构设计

## 总体架构概述

Yuan项目采用**分层通讯架构**：
- **自动触发层**：使用事件驱动架构处理必须执行的操作（如数据保存、记忆形成）
- **AI工具层**：使用MCP协议提供AI可选择调用的工具（如记忆检索、文件操作）

实现极致模块化和完全解耦的设计目标。

## 架构层次图

```
┌─────────────────────────────────────────────────────────────┐
│                    Web交互界面层                              │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/WebSocket
┌─────────────────────▼───────────────────────────────────────┐
│                  AI核心层 (MCP Client)                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            AiChatCore (核心对话模块)                     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ MCP Protocol (JSON-RPC)
┌─────────────────────▼───────────────────────────────────────┐
│                   MCP协议层                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              MCP Router & Manager                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────┬─────────┬─────────┬─────────┬───────────────────────────┘
      │         │         │         │
┌─────▼───┐ ┌───▼───┐ ┌───▼───┐ ┌───▼───┐
│Memory   │ │ File  │ │Network│ │System │
│Query    │ │MCP    │ │MCP    │ │MCP    │
│MCP      │ │Server │ │Server │ │Server │
│Server   │ └───────┘ └───────┘ └───────┘
└─────────┘     │         │         │
      │    ┌────▼─────────▼─────────▼───────────────────────────┐
      │    │                   事件总线层                        │
      │    │  ┌─────────────────────────────────────────────────┐ │
      │    │  │              Event Bus & Coordinator            │ │
      │    │  └─────────────────────────────────────────────────┘ │
      │    └─────┬─────────┬─────────┬─────────┬─────────────────┘
      │          │         │         │         │
      │    ┌─────▼───┐ ┌───▼───┐ ┌───▼───┐ ┌───▼───┐
      │    │Database │ │Memory │ │ Log   │ │Status │
      │    │Auto     │ │Auto   │ │Auto   │ │Auto   │
      │    │Save     │ │Store  │ │Record │ │Update │
      │    └─────────┘ └───────┘ └───────┘ └───────┘
      │          │         │         │         │
      └──────────┼─────────┼─────────┼─────────┼─────────────────┐
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   基础设施层                                  │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐            │
│  │ SQLite  │ │ Qdrant  │ │文件系统 │ │ 日志系统│            │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘            │
└─────────────────────────────────────────────────────────────┘
```

## 核心设计原则

### 1. 分层通讯策略
- **自动触发操作**：使用事件驱动架构，确保关键操作（如数据保存）必定执行
- **AI工具调用**：使用MCP协议，提供AI可选择的工具能力
- **明确职责边界**：自动的归自动，可选的归可选

### 2. 记忆模型设计
- **记忆形成**：对话内容自动保存和处理，AI无法选择不保存
- **记忆调用**：AI可主动检索和使用已有记忆
- **符合人类认知**：记忆形成是被动的，记忆使用是主动的

### 3. 极致模块化
- 每个功能模块独立开发、测试、部署
- 模块间零直接依赖
- 支持热插拔和动态扩展

## 模块分类规划

### 自动触发模块（事件驱动）

#### 数据库自动保存模块
**功能**：对话历史的自动存储
- 监听对话完成事件
- 自动保存所有对话内容
- 无需AI主动调用

#### 记忆自动形成模块
**功能**：从对话中自动提取和存储记忆
- 监听对话完成事件
- 自动分析和提取关键信息
- 自动建立语义索引

### AI工具模块（MCP协议）

#### 记忆检索MCP Server
**功能**：AI可主动调用的记忆检索工具
```json
{
  "tools": [
    "search_memory",
    "get_related_memories",
    "query_conversation_history"
  ],
  "resources": [
    "memory://semantic",
    "memory://episodic",
    "conversation://history"
  ]
}
```

### 文件MCP Server
**功能**：文件操作和管理
```json
{
  "tools": [
    "read_file",
    "write_file", 
    "list_directory",
    "file_operations"
  ],
  "resources": [
    "file://workspace",
    "file://uploads"
  ]
}
```

### 网络MCP Server
**功能**：网络请求和外部API调用
```json
{
  "tools": [
    "http_request",
    "web_search",
    "api_call",
    "download_file"
  ],
  "resources": [
    "web://search",
    "api://external"
  ]
}
```

### 系统MCP Server
**功能**：系统状态监控和控制
```json
{
  "tools": [
    "get_system_status",
    "monitor_resources",
    "execute_command",
    "manage_processes"
  ],
  "resources": [
    "system://status",
    "system://logs"
  ]
}
```

## 通讯协议设计

### 对话处理完整流程
```
1. 用户输入 → AI核心模块
2. AI分析是否需要调用工具
3. 如需要：通过MCP协议调用工具 → 获取信息
4. AI生成回复
5. 自动触发：发布"对话完成"事件
6. 数据库模块：自动保存对话
7. 记忆模块：自动提取和存储记忆
8. 返回回复给用户
```

### MCP工具调用流程
```
1. AI分析用户需求
2. 选择合适的MCP工具
3. 调用MCP server获取信息
4. 整合信息生成回复
```

### 事件驱动流程
```
1. AI对话完成后自动发布事件
2. 事件总线接收并路由事件
3. 相关模块订阅并自动处理
4. 确保关键操作必定执行
```

## 技术实现规范

### MCP Server实现标准
- 基于Python的MCP server框架
- 统一的错误处理和日志记录
- 标准化的配置管理
- 完整的工具和资源定义

### 事件总线实现
- 异步事件处理机制
- 事件持久化和重试机制
- 事件过滤和路由规则
- 性能监控和调试支持

### 配置管理
- 统一的配置文件格式
- 环境变量支持
- 动态配置更新
- 配置验证和默认值

## 扩展性设计

### 新增MCP Server
1. 实现标准MCP协议接口
2. 定义工具和资源清单
3. 注册到MCP路由器
4. 更新系统配置

### 事件扩展
1. 定义新的事件类型
2. 实现事件处理器
3. 注册到事件总线
4. 配置事件路由规则

## 开发实施路径

### 第一阶段：基础架构
1. 实现MCP协议层和路由器
2. 创建事件总线系统
3. 建立配置管理机制

### 第二阶段：核心功能模块
1. 数据库自动保存模块（事件驱动）
2. 记忆自动形成模块（事件驱动）
3. 记忆检索MCP Server（AI工具）

### 第三阶段：扩展MCP工具
1. 文件操作MCP Server
2. 网络请求MCP Server
3. 系统操作MCP Server

### 第四阶段：优化完善
1. 性能优化
2. 监控和调试工具
3. 文档和测试完善

## 架构优势

✅ **职责明确**：自动操作和可选操作分离，符合认知模型
✅ **数据可靠**：关键数据保存通过事件驱动，确保不丢失
✅ **AI灵活**：AI可根据需要选择调用工具，提高效率
✅ **标准化**：MCP协议保证工具接口的标准化
✅ **模块化**：每个功能模块完全独立
✅ **可扩展**：易于添加新的自动模块和MCP工具
✅ **可测试**：模块独立，便于单元测试
✅ **高性能**：异步处理和事件驱动
✅ **易维护**：清晰的架构层次和职责分离
