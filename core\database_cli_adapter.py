#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库CLI适配器
通过命令行工具调用数据库操作，实现完全解耦的架构
"""

import json
import subprocess
import asyncio
from typing import List, Dict, Optional, Any
from pathlib import Path


class DatabaseCLIAdapter:
    """数据库CLI适配器 - 通过命令行工具操作数据库"""
    
    def __init__(self, cli_tool_path: str = None):
        """
        初始化CLI适配器
        
        Args:
            cli_tool_path: CLI工具路径，默认为项目根目录下的工具
        """
        if cli_tool_path is None:
            # 默认路径：项目根目录/database/tools/cli_tool.py
            project_root = Path(__file__).parent.parent
            cli_tool_path = project_root / "database" / "tools" / "cli_tool.py"
        
        self.cli_tool_path = str(cli_tool_path)
        # 使用虚拟环境的Python路径
        project_root = Path(__file__).parent.parent
        venv_python = project_root / "venv" / "Scripts" / "python.exe"
        self.python_cmd = str(venv_python) if venv_python.exists() else "python"
    
    async def _run_cli_command(self, args: List[str]) -> Dict[str, Any]:
        """
        执行CLI命令
        
        Args:
            args: 命令行参数列表
            
        Returns:
            包含结果和状态的字典
        """
        cmd = [self.python_cmd, "-m", "database.tools.cli_tool"] + args
        
        try:
            # 异步执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=Path(self.cli_tool_path).parent.parent.parent  # 项目根目录
            )
            
            stdout, stderr = await process.communicate()
            
            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8', errors='ignore'),
                "stderr": stderr.decode('utf-8', errors='ignore')
            }
            
        except Exception as e:
            return {
                "success": False,
                "returncode": -1,
                "stdout": "",
                "stderr": f"执行命令失败: {str(e)}"
            }
    
    def _escape_json_arg(self, data: Any) -> str:
        """转义JSON参数用于命令行传递"""
        if data is None:
            return ""
        return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
    
    async def save_conversation(self, session_id: str, role: str, content: str,
                               attachments: Optional[List[Dict]] = None,
                               metadata: Optional[Dict] = None) -> bool:
        """
        保存对话记录
        
        Args:
            session_id: 会话ID
            role: 角色
            content: 内容
            attachments: 附件列表
            metadata: 元数据
            
        Returns:
            是否成功
        """
        args = [
            "--insert-conversation",
            "--session-id", session_id,
            "--role", role,
            "--content", content
        ]
        
        if attachments:
            args.extend(["--attachments", self._escape_json_arg(attachments)])
        
        if metadata:
            args.extend(["--metadata", self._escape_json_arg(metadata)])
        
        result = await self._run_cli_command(args)
        
        if not result["success"]:
            print(f"保存对话失败: {result['stderr']}")
            print(f"命令输出: {result['stdout']}")
            return False
        
        return "成功插入对话记录" in result["stdout"]
    
    async def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict]:
        """
        获取对话历史
        
        Args:
            session_id: 会话ID
            limit: 限制数量
            
        Returns:
            对话历史列表
        """
        args = [
            "--table", "conversations",
            "--session-id", session_id,
            "--limit", str(limit),
            "--format", "json"
        ]
        
        result = await self._run_cli_command(args)
        
        if not result["success"]:
            print(f"查询对话历史失败: {result['stderr']}")
            return []
        
        try:
            # 解析JSON输出
            data = json.loads(result["stdout"])
            return data if isinstance(data, list) else []
        except json.JSONDecodeError as e:
            print(f"解析JSON失败: {e}")
            return []
    
    async def get_database_status(self) -> Dict[str, Any]:
        """
        获取数据库状态
        
        Returns:
            数据库状态信息
        """
        args = ["--status", "--format", "json"]
        
        result = await self._run_cli_command(args)
        
        if not result["success"]:
            print(f"获取数据库状态失败: {result['stderr']}")
            return {}
        
        try:
            return json.loads(result["stdout"])
        except json.JSONDecodeError:
            return {}
    
    async def export_conversations(self, session_id: str = None, 
                                  limit: int = 1000, filename: str = None) -> bool:
        """
        导出对话记录
        
        Args:
            session_id: 会话ID（可选）
            limit: 限制数量
            filename: 导出文件名
            
        Returns:
            是否成功
        """
        args = ["--export", "conversations", "--limit", str(limit)]
        
        if filename:
            args.extend(["--filename", filename])
        
        result = await self._run_cli_command(args)
        
        return result["success"] and "数据已导出" in result["stdout"]
    
    async def execute_query(self, sql: str) -> List[Dict]:
        """
        执行自定义SQL查询
        
        Args:
            sql: SQL查询语句
            
        Returns:
            查询结果
        """
        args = ["--query", sql, "--format", "json"]
        
        result = await self._run_cli_command(args)
        
        if not result["success"]:
            print(f"执行查询失败: {result['stderr']}")
            return []
        
        try:
            return json.loads(result["stdout"])
        except json.JSONDecodeError:
            return []
    
    async def cleanup_old_data(self, days: int = 30) -> bool:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            是否成功
        """
        args = ["--cleanup", str(days), "--confirm"]
        
        result = await self._run_cli_command(args)
        
        return result["success"] and "已清理" in result["stdout"]


# 使用示例
async def example_usage():
    """使用示例"""
    # 创建适配器
    db = DatabaseCLIAdapter()
    
    print("🧪 测试CLI适配器功能")
    print("=" * 40)
    
    # 1. 保存对话记录
    print("\n1. 保存对话记录...")
    success = await db.save_conversation(
        session_id="cli_test_001",
        role="user",
        content="通过CLI适配器保存的消息",
        metadata={"method": "cli_adapter", "test": True}
    )
    print(f"保存结果: {'成功' if success else '失败'}")
    
    # 2. 保存带附件的对话
    print("\n2. 保存带附件的对话...")
    attachments = [
        {
            "path": "/uploads/cli_test.jpg",
            "size": 1024,
            "name": "CLI测试图片.jpg",
            "mime_type": "image/jpeg"
        }
    ]
    success = await db.save_conversation(
        session_id="cli_test_001",
        role="user",
        content="这是通过CLI适配器保存的带附件消息",
        attachments=attachments
    )
    print(f"保存结果: {'成功' if success else '失败'}")
    
    # 3. 获取对话历史
    print("\n3. 获取对话历史...")
    history = await db.get_conversation_history("cli_test_001")
    print(f"获取到 {len(history)} 条对话记录")
    for i, conv in enumerate(history, 1):
        print(f"  {i}. [{conv.get('role')}] {conv.get('content', '')[:30]}...")
        if conv.get('attachments') and conv['attachments'] != 'null':
            print(f"     📎 包含附件")
    
    # 4. 获取数据库状态
    print("\n4. 获取数据库状态...")
    status = await db.get_database_status()
    if status:
        print(f"总记录数: {status.get('total_records', 0)}")
        print(f"表统计: {status.get('table_stats', {})}")
    
    # 5. 执行自定义查询
    print("\n5. 执行自定义查询...")
    results = await db.execute_query(
        "SELECT session_id, COUNT(*) as count FROM conversations GROUP BY session_id"
    )
    print(f"查询结果: {results}")


if __name__ == "__main__":
    asyncio.run(example_usage())
