#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志微服务 - 独立数据库版本
只维护日志相关的数据，拥有自己的SQLite数据库
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from fastapi import HTTPException, Query
from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.sql import func

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from microservices_v2.base_service import BaseService

# 数据库模型
Base = declarative_base()


class LogEntry(Base):
    """日志记录表 - 仅日志相关数据"""
    __tablename__ = 'logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    level = Column(String(20), nullable=False, index=True, comment='日志级别')
    module = Column(String(100), nullable=False, index=True, comment='模块名称')
    message = Column(Text, nullable=False, comment='日志消息')
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True, comment='时间戳')
    function_name = Column(String(100), comment='函数名称')
    line_number = Column(Integer, comment='行号')
    exception_info = Column(Text, comment='异常信息')
    extra_data = Column(JSON, comment='额外数据')
    service_name = Column(String(50), comment='服务名称')
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')


class LogStats(Base):
    """日志统计表"""
    __tablename__ = 'log_stats'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(String(10), nullable=False, index=True, comment='日期 YYYY-MM-DD')
    level = Column(String(20), nullable=False, comment='日志级别')
    service_name = Column(String(50), comment='服务名称')
    count = Column(Integer, default=0, comment='数量')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')


# 请求/响应模型
class LogRequest(BaseModel):
    level: str
    module: str
    message: str
    function_name: Optional[str] = None
    line_number: Optional[int] = None
    exception_info: Optional[str] = None
    extra_data: Optional[Dict] = None
    service_name: Optional[str] = None


class LogService(BaseService):
    """日志微服务"""
    
    def __init__(self):
        super().__init__("log", 8003)
        
        # 独立的数据库 - 只存储日志相关数据
        self.db_path = Path("data/log_service.db")
        self.db_path.parent.mkdir(exist_ok=True)
        
        # 创建异步数据库引擎
        self.async_engine = create_async_engine(
            f"sqlite+aiosqlite:///{self.db_path}",
            echo=False
        )
        self.async_session_maker = async_sessionmaker(
            self.async_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        self.setup_log_routes()
    
    async def initialize_database(self):
        """初始化数据库"""
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print(f"✅ 日志服务数据库初始化完成: {self.db_path}")
    
    async def save_log(self, level: str, module: str, message: str,
                      function_name: Optional[str] = None,
                      line_number: Optional[int] = None,
                      exception_info: Optional[str] = None,
                      extra_data: Optional[Dict] = None,
                      service_name: Optional[str] = None):
        """保存日志记录"""
        async with self.async_session_maker() as session:
            log_entry = LogEntry(
                level=level.upper(),
                module=module,
                message=message,
                function_name=function_name,
                line_number=line_number,
                exception_info=exception_info,
                extra_data=extra_data,
                service_name=service_name
            )
            session.add(log_entry)
            await session.commit()
            
            # 更新统计信息
            await self.update_log_stats(level, service_name)
    
    async def update_log_stats(self, level: str, service_name: Optional[str] = None):
        """更新日志统计"""
        today = datetime.now().strftime('%Y-%m-%d')
        
        async with self.async_session_maker() as session:
            from sqlalchemy import select, update
            
            # 查找现有统计记录
            stmt = select(LogStats).where(
                LogStats.date == today,
                LogStats.level == level.upper(),
                LogStats.service_name == service_name
            )
            result = await session.execute(stmt)
            stats = result.scalar_one_or_none()
            
            if stats:
                # 更新现有记录
                stats.count += 1
                stats.updated_at = datetime.now()
            else:
                # 创建新记录
                stats = LogStats(
                    date=today,
                    level=level.upper(),
                    service_name=service_name,
                    count=1
                )
                session.add(stats)
            
            await session.commit()
    
    async def get_logs(self, level: Optional[str] = None,
                      service_name: Optional[str] = None,
                      hours: int = 24,
                      limit: int = 100) -> List[Dict]:
        """获取日志记录"""
        async with self.async_session_maker() as session:
            from sqlalchemy import select, and_
            
            # 构建查询条件
            conditions = []
            
            if level:
                conditions.append(LogEntry.level == level.upper())
            
            if service_name:
                conditions.append(LogEntry.service_name == service_name)
            
            # 时间范围
            time_threshold = datetime.now() - timedelta(hours=hours)
            conditions.append(LogEntry.timestamp >= time_threshold)
            
            # 构建查询
            stmt = select(LogEntry)
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            stmt = stmt.order_by(LogEntry.timestamp.desc()).limit(limit)
            
            result = await session.execute(stmt)
            logs = result.scalars().all()
            
            return [
                {
                    "id": log.id,
                    "level": log.level,
                    "module": log.module,
                    "message": log.message,
                    "timestamp": log.timestamp.isoformat() if log.timestamp else None,
                    "function_name": log.function_name,
                    "line_number": log.line_number,
                    "exception_info": log.exception_info,
                    "extra_data": log.extra_data,
                    "service_name": log.service_name
                }
                for log in logs
            ]
    
    def setup_log_routes(self):
        """设置日志相关路由"""
        
        @self.app.post("/logs")
        async def save_log_entry(request: LogRequest):
            """保存日志记录"""
            try:
                await self.save_log(
                    level=request.level,
                    module=request.module,
                    message=request.message,
                    function_name=request.function_name,
                    line_number=request.line_number,
                    exception_info=request.exception_info,
                    extra_data=request.extra_data,
                    service_name=request.service_name
                )
                
                return {
                    "success": True,
                    "message": "日志记录保存成功"
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"保存日志失败: {str(e)}")
        
        @self.app.get("/logs")
        async def get_log_entries(
            level: Optional[str] = Query(None, description="日志级别"),
            service_name: Optional[str] = Query(None, description="服务名称"),
            hours: int = Query(24, description="时间范围（小时）"),
            limit: int = Query(100, description="限制数量")
        ):
            """获取日志记录"""
            try:
                logs = await self.get_logs(level, service_name, hours, limit)
                return {
                    "success": True,
                    "data": logs,
                    "count": len(logs),
                    "filters": {
                        "level": level,
                        "service_name": service_name,
                        "hours": hours,
                        "limit": limit
                    }
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")
        
        @self.app.get("/logs/stats")
        async def get_log_statistics():
            """获取日志统计信息"""
            async with self.async_session_maker() as session:
                from sqlalchemy import select, func as sql_func
                
                # 总日志数
                total_logs = await session.execute(
                    select(sql_func.count(LogEntry.id))
                )
                total_count = total_logs.scalar()
                
                # 按级别统计
                level_stats = await session.execute(
                    select(LogEntry.level, sql_func.count(LogEntry.id))
                    .group_by(LogEntry.level)
                )
                level_data = {level: count for level, count in level_stats.fetchall()}
                
                # 按服务统计
                service_stats = await session.execute(
                    select(LogEntry.service_name, sql_func.count(LogEntry.id))
                    .group_by(LogEntry.service_name)
                )
                service_data = {service or "unknown": count for service, count in service_stats.fetchall()}
                
                # 最近24小时统计
                time_threshold = datetime.now() - timedelta(hours=24)
                recent_logs = await session.execute(
                    select(sql_func.count(LogEntry.id))
                    .where(LogEntry.timestamp >= time_threshold)
                )
                recent_count = recent_logs.scalar()
                
                return {
                    "total_logs": total_count,
                    "recent_24h": recent_count,
                    "level_distribution": level_data,
                    "service_distribution": service_data,
                    "database_file": str(self.db_path),
                    "service": "log"
                }
        
        @self.app.get("/logs/daily-stats")
        async def get_daily_statistics(days: int = Query(7, description="天数")):
            """获取每日统计"""
            async with self.async_session_maker() as session:
                from sqlalchemy import select
                
                # 获取最近N天的统计
                start_date = datetime.now() - timedelta(days=days)
                
                stmt = select(LogStats).where(
                    LogStats.date >= start_date.strftime('%Y-%m-%d')
                ).order_by(LogStats.date.desc())
                
                result = await session.execute(stmt)
                stats = result.scalars().all()
                
                # 按日期组织数据
                daily_data = {}
                for stat in stats:
                    if stat.date not in daily_data:
                        daily_data[stat.date] = {}
                    
                    key = f"{stat.level}_{stat.service_name or 'unknown'}"
                    daily_data[stat.date][key] = stat.count
                
                return {
                    "success": True,
                    "daily_stats": daily_data,
                    "days": days
                }
        
        @self.app.delete("/logs/cleanup")
        async def cleanup_old_logs(days: int = Query(30, description="保留天数")):
            """清理旧日志"""
            try:
                cutoff_date = datetime.now() - timedelta(days=days)
                
                async with self.async_session_maker() as session:
                    from sqlalchemy import delete
                    
                    # 删除旧日志
                    stmt = delete(LogEntry).where(LogEntry.timestamp < cutoff_date)
                    result = await session.execute(stmt)
                    deleted_count = result.rowcount
                    
                    # 删除旧统计
                    stats_stmt = delete(LogStats).where(
                        LogStats.date < cutoff_date.strftime('%Y-%m-%d')
                    )
                    stats_result = await session.execute(stats_stmt)
                    deleted_stats = stats_result.rowcount
                    
                    await session.commit()
                
                return {
                    "success": True,
                    "message": f"清理完成",
                    "deleted_logs": deleted_count,
                    "deleted_stats": deleted_stats,
                    "cutoff_date": cutoff_date.isoformat()
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")
        
        # 重写启动事件以包含数据库初始化
        @self.app.on_event("startup")
        async def startup_event():
            """服务启动事件"""
            print(f"🚀 {self.service_name} 服务启动中...")
            await self.initialize_database()
            await self.register_to_registry()
            self.start_heartbeat()
            
            # 记录服务启动日志
            await self.save_log(
                level="INFO",
                module="log_service",
                message="日志服务启动完成",
                service_name="log"
            )
            
            print(f"✅ {self.service_name} 服务启动完成")


def main():
    """主函数"""
    service = LogService()
    service.run()


if __name__ == "__main__":
    main()
