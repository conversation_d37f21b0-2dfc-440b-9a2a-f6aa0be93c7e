# Yuan项目微服务架构 v2.0

## 🎯 核心特性

基于**服务注册中心**和**数据库分离**的真正微服务架构：

### ✨ 服务注册与发现
- 🏛️ **服务注册中心** - 统一的服务注册和发现
- 🔄 **自动注册** - 服务启动时自动注册到注册中心
- 💓 **心跳监控** - 定期心跳检测服务健康状态
- 🔍 **服务发现** - 动态发现和调用其他服务
- 🧹 **自动清理** - 自动清理失效的服务实例

### 🗄️ 数据库完全分离
- 📊 **独立数据库** - 每个服务拥有自己的SQLite数据库
- 🔒 **数据隔离** - 服务间数据完全隔离，无共享
- 📈 **专用存储** - 每个服务只存储相关的业务数据
- 🎯 **职责单一** - 数据库设计专注于单一业务领域

## 🏗️ 架构图

```
┌─────────────────────────────────────────────────────────┐
│              Service Registry (8500)                    │
│                 服务注册中心                             │
└─────────────────────┬───────────────────────────────────┘
                      │ 注册/发现/心跳
        ┌─────────────┼─────────────┐
        │             │             │
┌───────▼──────┐ ┌────▼────┐ ┌─────▼─────┐
│ Chat Service │ │   Log   │ │  Memory   │
│    (8002)    │ │ Service │ │ Service   │
│              │ │ (8003)  │ │ (8004)    │
│ chat_db.db   │ │ log.db  │ │memory.db  │
└──────────────┘ └─────────┘ └───────────┘
```

## 📋 服务详情

### 🏛️ Service Registry (8500)
**功能**: 服务注册中心
- 服务注册和注销
- 服务发现和负载均衡
- 健康检查和监控
- 自动清理失效服务

**数据**: 内存存储服务信息

### 🤖 Chat Service (8002)
**功能**: AI对话处理
- 对话生成和管理
- 会话状态维护
- 上下文处理

**独立数据库**: `data/chat_service.db`
- `conversations` - 对话记录
- `chat_sessions` - 会话信息

### 📝 Log Service (8003)
**功能**: 日志记录和管理
- 日志收集和存储
- 日志查询和统计
- 日志清理和归档

**独立数据库**: `data/log_service.db`
- `logs` - 日志记录
- `log_stats` - 日志统计

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install fastapi uvicorn httpx aiosqlite
```

### 2. 启动服务集群
```bash
# 启动所有服务
python microservices_v2/start_all.py

# 服务启动顺序：
# 1. Service Registry (8500)
# 2. Log Service (8003) 
# 3. Chat Service (8002)
```

### 3. 验证服务状态
```bash
# 检查注册中心
curl http://127.0.0.1:8500/health

# 查看已注册服务
curl http://127.0.0.1:8500/services

# 服务发现
curl http://127.0.0.1:8500/discover/chat
curl http://127.0.0.1:8500/discover/log
```

### 4. 运行集成测试
```bash
python microservices_v2/test_client.py
```

## 🔄 服务通信流程

### 1. 服务启动和注册
```python
# 服务启动时自动注册
service = ChatService()
await service.register_to_registry()
service.start_heartbeat()
```

### 2. 服务发现和调用
```python
# 发现其他服务
log_services = await service.discover_service("log")

# 调用其他服务
await service.call_service("log", "/logs", "POST", {
    "level": "INFO",
    "message": "处理对话请求"
})
```

### 3. 数据库操作
```python
# 每个服务操作自己的数据库
async with self.async_session_maker() as session:
    conversation = Conversation(...)
    session.add(conversation)
    await session.commit()
```

## 📊 数据库设计

### Chat Service Database
```sql
-- 对话记录表
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    attachments JSON,
    meta_data JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 会话信息表
CREATE TABLE chat_sessions (
    id INTEGER PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    session_name VARCHAR(500),
    status VARCHAR(20) DEFAULT 'active',
    message_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Log Service Database
```sql
-- 日志记录表
CREATE TABLE logs (
    id INTEGER PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    module VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    function_name VARCHAR(100),
    line_number INTEGER,
    exception_info TEXT,
    extra_data JSON,
    service_name VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 日志统计表
CREATE TABLE log_stats (
    id INTEGER PRIMARY KEY,
    date VARCHAR(10) NOT NULL,
    level VARCHAR(20) NOT NULL,
    service_name VARCHAR(50),
    count INTEGER DEFAULT 0,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 API接口

### Service Registry API
```bash
# 注册服务
POST /register
{
    "service_name": "chat",
    "host": "127.0.0.1",
    "port": 8002,
    "health_check_url": "http://127.0.0.1:8002/health"
}

# 服务发现
GET /discover/{service_name}

# 心跳更新
POST /heartbeat
{
    "service_id": "uuid",
    "status": "healthy"
}
```

### Chat Service API
```bash
# 发送对话
POST /chat
{
    "session_id": "optional",
    "message": "Hello",
    "model": "gpt-4"
}

# 获取会话历史
GET /sessions/{session_id}/history

# 获取统计信息
GET /stats
```

### Log Service API
```bash
# 记录日志
POST /logs
{
    "level": "INFO",
    "module": "chat_service",
    "message": "处理对话请求"
}

# 查询日志
GET /logs?level=ERROR&hours=24

# 获取统计
GET /logs/stats
```

## 🎯 架构优势

### ✅ 完全解耦
- 每个服务独立开发、测试、部署
- 服务间零代码依赖
- 故障隔离，单点故障不影响整体

### ✅ 数据隔离
- 每个服务拥有独立的数据库
- 数据安全性和一致性保障
- 便于数据备份和迁移

### ✅ 动态服务管理
- 服务自动注册和发现
- 健康检查和故障转移
- 支持服务的动态扩缩容

### ✅ 技术栈自由
- 每个服务可以使用不同技术
- 独立的版本控制和发布
- 便于技术栈升级和迁移

## ⚠️ 注意事项

### 网络延迟
- 服务间调用存在网络开销
- 建议合理设计API粒度
- 考虑使用缓存减少调用

### 数据一致性
- 分布式事务处理复杂
- 建议使用最终一致性模型
- 重要操作考虑补偿机制

### 运维复杂度
- 需要监控多个服务状态
- 日志分散，需要集中管理
- 部署和配置相对复杂

## 🔮 扩展计划

### 即将实现
- Memory Service (记忆管理)
- Tools Service (工具集成)
- Config Service (配置管理)

### 未来增强
- 服务网格 (Service Mesh)
- 分布式追踪 (Distributed Tracing)
- 配置中心 (Configuration Center)
- 熔断器 (Circuit Breaker)

---

**总结**: 这是一个真正的微服务架构实现，具备服务注册发现、数据库分离、自动化运维等核心特性，为Yuan项目提供了强大的扩展性和可维护性基础。
