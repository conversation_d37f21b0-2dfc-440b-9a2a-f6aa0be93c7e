#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务注册中心
实现服务的动态注册、发现和健康检查
"""

import asyncio
import time
import uuid
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
import json


@dataclass
class ServiceInfo:
    """服务信息"""
    service_id: str
    service_name: str
    host: str
    port: int
    health_check_url: str
    metadata: Dict
    registered_at: float
    last_heartbeat: float
    status: str = "healthy"  # healthy, unhealthy, unknown


class ServiceRegistryRequest(BaseModel):
    """服务注册请求"""
    service_name: str
    host: str
    port: int
    health_check_url: str
    metadata: Dict = {}


class HeartbeatRequest(BaseModel):
    """心跳请求"""
    service_id: str
    status: str = "healthy"
    metadata: Dict = {}


class ServiceRegistry:
    """服务注册中心"""
    
    def __init__(self):
        self.app = FastAPI(
            title="Yuan Service Registry",
            description="Yuan项目服务注册中心",
            version="1.0.0"
        )
        
        # 服务存储：service_id -> ServiceInfo
        self.services: Dict[str, ServiceInfo] = {}
        
        # 服务名称索引：service_name -> List[service_id]
        self.service_index: Dict[str, List[str]] = {}
        
        # 健康检查配置
        self.heartbeat_timeout = 30  # 30秒无心跳则标记为不健康
        self.cleanup_timeout = 300   # 5分钟无心跳则移除服务
        
        self.setup_routes()
        self.start_background_tasks()
    
    def setup_routes(self):
        """设置路由"""
        
        @self.app.post("/register")
        async def register_service(request: ServiceRegistryRequest):
            """注册服务"""
            service_id = str(uuid.uuid4())
            current_time = time.time()
            
            service_info = ServiceInfo(
                service_id=service_id,
                service_name=request.service_name,
                host=request.host,
                port=request.port,
                health_check_url=request.health_check_url,
                metadata=request.metadata,
                registered_at=current_time,
                last_heartbeat=current_time,
                status="healthy"
            )
            
            # 存储服务信息
            self.services[service_id] = service_info
            
            # 更新服务名称索引
            if request.service_name not in self.service_index:
                self.service_index[request.service_name] = []
            self.service_index[request.service_name].append(service_id)
            
            print(f"✅ 服务注册成功: {request.service_name} ({service_id[:8]})")
            
            return {
                "success": True,
                "service_id": service_id,
                "message": f"服务 {request.service_name} 注册成功"
            }
        
        @self.app.post("/heartbeat")
        async def heartbeat(request: HeartbeatRequest):
            """服务心跳"""
            if request.service_id not in self.services:
                raise HTTPException(status_code=404, detail="服务不存在")
            
            service = self.services[request.service_id]
            service.last_heartbeat = time.time()
            service.status = request.status
            service.metadata.update(request.metadata)
            
            return {"success": True, "message": "心跳更新成功"}
        
        @self.app.get("/discover/{service_name}")
        async def discover_service(service_name: str, healthy_only: bool = True):
            """服务发现"""
            if service_name not in self.service_index:
                return {"services": [], "count": 0}
            
            service_ids = self.service_index[service_name]
            services = []
            
            for service_id in service_ids:
                if service_id in self.services:
                    service = self.services[service_id]
                    
                    # 过滤健康状态
                    if healthy_only and service.status != "healthy":
                        continue
                    
                    services.append({
                        "service_id": service.service_id,
                        "host": service.host,
                        "port": service.port,
                        "url": f"http://{service.host}:{service.port}",
                        "health_check_url": service.health_check_url,
                        "status": service.status,
                        "metadata": service.metadata,
                        "last_heartbeat": service.last_heartbeat
                    })
            
            return {"services": services, "count": len(services)}
        
        @self.app.get("/services")
        async def list_all_services():
            """列出所有服务"""
            result = {}
            
            for service_name, service_ids in self.service_index.items():
                services = []
                for service_id in service_ids:
                    if service_id in self.services:
                        service = self.services[service_id]
                        services.append(asdict(service))
                result[service_name] = services
            
            return result
        
        @self.app.delete("/unregister/{service_id}")
        async def unregister_service(service_id: str):
            """注销服务"""
            if service_id not in self.services:
                raise HTTPException(status_code=404, detail="服务不存在")
            
            service = self.services[service_id]
            service_name = service.service_name
            
            # 从服务存储中移除
            del self.services[service_id]
            
            # 从索引中移除
            if service_name in self.service_index:
                self.service_index[service_name].remove(service_id)
                if not self.service_index[service_name]:
                    del self.service_index[service_name]
            
            print(f"🗑️ 服务注销: {service_name} ({service_id[:8]})")
            
            return {"success": True, "message": f"服务 {service_id} 注销成功"}
        
        @self.app.get("/health")
        async def health_check():
            """注册中心健康检查"""
            return {
                "status": "healthy",
                "service": "registry",
                "registered_services": len(self.services),
                "service_types": len(self.service_index)
            }
    
    def start_background_tasks(self):
        """启动后台任务"""
        
        async def health_monitor():
            """健康监控任务"""
            while True:
                try:
                    current_time = time.time()
                    unhealthy_services = []
                    expired_services = []
                    
                    for service_id, service in self.services.items():
                        time_since_heartbeat = current_time - service.last_heartbeat
                        
                        if time_since_heartbeat > self.cleanup_timeout:
                            # 超过清理时间，移除服务
                            expired_services.append(service_id)
                        elif time_since_heartbeat > self.heartbeat_timeout:
                            # 超过心跳时间，标记为不健康
                            if service.status == "healthy":
                                service.status = "unhealthy"
                                unhealthy_services.append(service)
                    
                    # 清理过期服务
                    for service_id in expired_services:
                        service = self.services[service_id]
                        print(f"🧹 清理过期服务: {service.service_name} ({service_id[:8]})")
                        
                        # 从索引中移除
                        if service.service_name in self.service_index:
                            self.service_index[service.service_name].remove(service_id)
                            if not self.service_index[service.service_name]:
                                del self.service_index[service.service_name]
                        
                        del self.services[service_id]
                    
                    # 报告不健康服务
                    for service in unhealthy_services:
                        print(f"⚠️ 服务不健康: {service.service_name} ({service.service_id[:8]})")
                    
                    await asyncio.sleep(10)  # 每10秒检查一次
                    
                except Exception as e:
                    print(f"❌ 健康监控错误: {e}")
                    await asyncio.sleep(10)
        
        # 启动后台任务
        asyncio.create_task(health_monitor())
    
    def run(self, host: str = "127.0.0.1", port: int = 8500):
        """运行注册中心"""
        print("🏛️ 启动Yuan服务注册中心")
        print(f"📍 注册中心地址: http://{host}:{port}")
        print("📋 主要功能:")
        print("   - 服务注册: POST /register")
        print("   - 服务发现: GET /discover/{service_name}")
        print("   - 心跳更新: POST /heartbeat")
        print("   - 服务列表: GET /services")
        
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )


# 服务注册中心实例
registry = ServiceRegistry()

if __name__ == "__main__":
    registry.run()
