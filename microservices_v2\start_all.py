#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微服务集群启动脚本 - 服务注册中心版本
"""

import asyncio
import subprocess
import time
import sys
import signal
from pathlib import Path


class MicroserviceCluster:
    """微服务集群管理器"""
    
    def __init__(self):
        self.services = [
            {
                "name": "registry",
                "script": "service_registry.py",
                "port": 8500,
                "process": None,
                "description": "服务注册中心"
            },
            {
                "name": "log",
                "script": "log_service.py",
                "port": 8003,
                "process": None,
                "description": "日志服务"
            },
            {
                "name": "chat",
                "script": "chat_service.py",
                "port": 8002,
                "process": None,
                "description": "AI对话服务"
            }
        ]
        self.base_dir = Path(__file__).parent
    
    def start_service(self, service: dict):
        """启动单个服务"""
        script_path = self.base_dir / service["script"]
        
        print(f"🚀 启动 {service['description']} (端口 {service['port']})...")
        
        try:
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.base_dir.parent,  # 项目根目录
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == "win32" else 0
            )
            
            service["process"] = process
            print(f"✅ {service['description']} 启动成功 (PID: {process.pid})")
            return True
            
        except Exception as e:
            print(f"❌ {service['description']} 启动失败: {e}")
            return False
    
    def stop_service(self, service: dict):
        """停止单个服务"""
        if service["process"] and service["process"].poll() is None:
            print(f"🛑 停止 {service['description']}...")
            
            try:
                if sys.platform == "win32":
                    subprocess.run(
                        ["taskkill", "/F", "/T", "/PID", str(service["process"].pid)],
                        check=False
                    )
                else:
                    service["process"].terminate()
                    service["process"].wait(timeout=5)
                
                print(f"✅ {service['description']} 已停止")
                
            except Exception as e:
                print(f"⚠️ 停止 {service['description']} 时出错: {e}")
            
            service["process"] = None
    
    def start_all(self):
        """启动所有服务"""
        print("🌟 启动Yuan微服务集群 (服务注册中心版本)")
        print("=" * 60)
        
        for service in self.services:
            if self.start_service(service):
                # 等待服务启动
                if service["name"] == "registry":
                    time.sleep(3)  # 注册中心需要更多时间
                else:
                    time.sleep(2)
            else:
                print("❌ 服务启动失败，停止启动流程")
                self.stop_all()
                return False
        
        print("\n" + "=" * 60)
        print("🎉 所有微服务启动完成！")
        print("\n📍 服务地址:")
        for service in self.services:
            print(f"   {service['description']}: http://127.0.0.1:{service['port']}")
        
        print("\n🏛️ 服务注册中心: http://127.0.0.1:8500")
        print("📋 服务发现示例:")
        print("   curl http://127.0.0.1:8500/discover/chat")
        print("   curl http://127.0.0.1:8500/discover/log")
        print("\n按 Ctrl+C 停止所有服务")
        
        return True
    
    def stop_all(self):
        """停止所有服务"""
        print("\n🛑 停止所有微服务...")
        
        # 反向停止服务
        for service in reversed(self.services):
            self.stop_service(service)
        
        print("✅ 所有微服务已停止")
    
    def check_services(self):
        """检查服务状态"""
        print("\n📊 服务状态检查:")
        for service in self.services:
            if service["process"] and service["process"].poll() is None:
                print(f"   ✅ {service['description']}: 运行中 (PID: {service['process'].pid})")
            else:
                print(f"   ❌ {service['description']}: 已停止")
    
    def run(self):
        """运行服务集群"""
        def signal_handler(signum, frame):
            print("\n收到停止信号...")
            self.stop_all()
            sys.exit(0)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            if self.start_all():
                # 保持运行状态
                while True:
                    time.sleep(5)
                    
                    # 检查服务是否还在运行
                    all_running = True
                    for service in self.services:
                        if not service["process"] or service["process"].poll() is not None:
                            print(f"⚠️ 检测到 {service['description']} 异常退出")
                            all_running = False
                    
                    if not all_running:
                        print("❌ 部分服务异常，停止所有服务")
                        break
            
        except KeyboardInterrupt:
            print("\n用户中断...")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            self.stop_all()


def main():
    """主函数"""
    print("🔧 Yuan微服务集群管理器 (服务注册中心版本)")
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "start":
            manager = MicroserviceCluster()
            manager.run()
        elif command == "stop":
            print("停止功能需要在运行中的管理器中使用 Ctrl+C")
        elif command == "status":
            manager = MicroserviceCluster()
            manager.check_services()
        else:
            print("用法: python start_all.py [start|stop|status]")
    else:
        # 默认启动所有服务
        manager = MicroserviceCluster()
        manager.run()


if __name__ == "__main__":
    main()
