#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微服务测试客户端 - 服务注册中心版本
测试服务注册、发现和相互调用
"""

import asyncio
import httpx
import json
import uuid
from typing import Dict, List


class MicroserviceTestClient:
    """微服务测试客户端"""
    
    def __init__(self, registry_url: str = "http://127.0.0.1:8500"):
        self.registry_url = registry_url
        self.session_id = str(uuid.uuid4())
    
    async def test_service_registry(self):
        """测试服务注册中心"""
        print("🏛️ 测试服务注册中心...")
        
        async with httpx.AsyncClient() as client:
            try:
                # 检查注册中心健康状态
                response = await client.get(f"{self.registry_url}/health")
                health = response.json()
                print(f"注册中心状态: {health['status']}")
                print(f"已注册服务数: {health['registered_services']}")
                
                # 列出所有服务
                response = await client.get(f"{self.registry_url}/services")
                services = response.json()
                print("已注册的服务:")
                for service_name, instances in services.items():
                    print(f"  {service_name}: {len(instances)} 个实例")
                
                return True
                
            except Exception as e:
                print(f"❌ 注册中心测试失败: {e}")
                return False
    
    async def test_service_discovery(self):
        """测试服务发现"""
        print("\n🔍 测试服务发现...")
        
        async with httpx.AsyncClient() as client:
            try:
                # 发现对话服务
                response = await client.get(f"{self.registry_url}/discover/chat")
                chat_services = response.json()
                print(f"发现对话服务: {chat_services['count']} 个实例")
                
                if chat_services['services']:
                    service = chat_services['services'][0]
                    print(f"  服务地址: {service['url']}")
                    print(f"  服务状态: {service['status']}")
                
                # 发现日志服务
                response = await client.get(f"{self.registry_url}/discover/log")
                log_services = response.json()
                print(f"发现日志服务: {log_services['count']} 个实例")
                
                if log_services['services']:
                    service = log_services['services'][0]
                    print(f"  服务地址: {service['url']}")
                    print(f"  服务状态: {service['status']}")
                
                return True
                
            except Exception as e:
                print(f"❌ 服务发现测试失败: {e}")
                return False
    
    async def get_service_url(self, service_name: str) -> str:
        """获取服务URL"""
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.registry_url}/discover/{service_name}")
            services = response.json()
            
            if services['services']:
                return services['services'][0]['url']
            else:
                raise Exception(f"未找到服务: {service_name}")
    
    async def test_log_service(self):
        """测试日志服务"""
        print("\n📝 测试日志服务...")
        
        try:
            log_url = await self.get_service_url("log")
            
            async with httpx.AsyncClient() as client:
                # 发送测试日志
                log_data = {
                    "level": "INFO",
                    "module": "test_client",
                    "message": "这是一条测试日志消息",
                    "service_name": "test_client",
                    "extra_data": {"test": True, "session_id": self.session_id}
                }
                
                response = await client.post(f"{log_url}/logs", json=log_data)
                if response.status_code == 200:
                    print("✅ 日志发送成功")
                else:
                    print(f"❌ 日志发送失败: {response.text}")
                    return False
                
                # 获取日志统计
                response = await client.get(f"{log_url}/logs/stats")
                if response.status_code == 200:
                    stats = response.json()
                    print(f"总日志数: {stats['total_logs']}")
                    print(f"最近24小时: {stats['recent_24h']}")
                    print(f"级别分布: {stats['level_distribution']}")
                else:
                    print(f"❌ 获取日志统计失败: {response.text}")
                
                # 查询最近日志
                response = await client.get(f"{log_url}/logs?limit=5")
                if response.status_code == 200:
                    logs = response.json()
                    print(f"最近日志: {logs['count']} 条")
                    for log in logs['data'][:3]:  # 显示前3条
                        print(f"  [{log['level']}] {log['module']}: {log['message'][:50]}...")
                
                return True
                
        except Exception as e:
            print(f"❌ 日志服务测试失败: {e}")
            return False
    
    async def test_chat_service(self):
        """测试对话服务"""
        print("\n🤖 测试对话服务...")
        
        try:
            chat_url = await self.get_service_url("chat")
            
            async with httpx.AsyncClient() as client:
                # 发送对话请求
                chat_data = {
                    "session_id": self.session_id,
                    "message": "你好，我正在测试微服务架构中的服务发现功能",
                    "model": "gpt-4"
                }
                
                response = await client.post(f"{chat_url}/chat", json=chat_data)
                if response.status_code == 200:
                    chat_response = response.json()
                    print("✅ 对话请求成功")
                    print(f"用户: {chat_response['message']}")
                    print(f"AI: {chat_response['response'][:100]}...")
                    print(f"会话ID: {chat_response['session_id']}")
                else:
                    print(f"❌ 对话请求失败: {response.text}")
                    return False
                
                # 获取对话统计
                response = await client.get(f"{chat_url}/stats")
                if response.status_code == 200:
                    stats = response.json()
                    print(f"总对话数: {stats['total_conversations']}")
                    print(f"总会话数: {stats['total_sessions']}")
                    print(f"角色分布: {stats['role_distribution']}")
                
                # 获取会话历史
                response = await client.get(f"{chat_url}/sessions/{self.session_id}/history")
                if response.status_code == 200:
                    history = response.json()
                    print(f"会话历史: {history['count']} 条记录")
                
                return True
                
        except Exception as e:
            print(f"❌ 对话服务测试失败: {e}")
            return False
    
    async def test_service_communication(self):
        """测试服务间通信"""
        print("\n🔄 测试服务间通信...")
        
        try:
            chat_url = await self.get_service_url("chat")
            log_url = await self.get_service_url("log")
            
            async with httpx.AsyncClient() as client:
                # 发送多轮对话，观察日志记录
                messages = [
                    "测试服务间通信功能",
                    "日志服务是否正常记录？",
                    "微服务架构运行如何？"
                ]
                
                for i, message in enumerate(messages, 1):
                    print(f"\n第 {i} 轮对话:")
                    
                    # 发送对话
                    chat_data = {
                        "session_id": self.session_id,
                        "message": message,
                        "model": "gpt-4"
                    }
                    
                    response = await client.post(f"{chat_url}/chat", json=chat_data)
                    if response.status_code == 200:
                        chat_response = response.json()
                        print(f"用户: {message}")
                        print(f"AI: {chat_response['response'][:80]}...")
                    
                    # 短暂延迟
                    await asyncio.sleep(1)
                
                # 检查日志记录
                print("\n检查日志记录:")
                response = await client.get(f"{log_url}/logs?service_name=chat&limit=10")
                if response.status_code == 200:
                    logs = response.json()
                    chat_logs = [log for log in logs['data'] if 'chat' in log.get('service_name', '')]
                    print(f"对话服务产生的日志: {len(chat_logs)} 条")
                    
                    for log in chat_logs[:3]:
                        print(f"  [{log['level']}] {log['message']}")
                
                return True
                
        except Exception as e:
            print(f"❌ 服务间通信测试失败: {e}")
            return False
    
    async def test_database_isolation(self):
        """测试数据库隔离"""
        print("\n🗄️ 测试数据库隔离...")
        
        try:
            chat_url = await self.get_service_url("chat")
            log_url = await self.get_service_url("log")
            
            async with httpx.AsyncClient() as client:
                # 获取对话服务统计
                response = await client.get(f"{chat_url}/stats")
                chat_stats = response.json()
                
                # 获取日志服务统计
                response = await client.get(f"{log_url}/logs/stats")
                log_stats = response.json()
                
                print("数据库隔离验证:")
                print(f"对话服务数据库: {chat_stats['database_file']}")
                print(f"  - 对话记录: {chat_stats['total_conversations']} 条")
                print(f"  - 会话数: {chat_stats['total_sessions']} 个")
                
                print(f"日志服务数据库: {log_stats['database_file']}")
                print(f"  - 日志记录: {log_stats['total_logs']} 条")
                print(f"  - 服务分布: {log_stats['service_distribution']}")
                
                # 验证数据库文件确实不同
                if chat_stats['database_file'] != log_stats['database_file']:
                    print("✅ 数据库隔离正常 - 每个服务使用独立的数据库")
                else:
                    print("❌ 数据库隔离异常 - 服务共享了数据库文件")
                    return False
                
                return True
                
        except Exception as e:
            print(f"❌ 数据库隔离测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 Yuan微服务架构集成测试")
        print("=" * 60)
        print("测试内容:")
        print("  - 服务注册中心功能")
        print("  - 服务发现机制")
        print("  - 独立数据库架构")
        print("  - 服务间通信")
        print("=" * 60)
        
        tests = [
            ("服务注册中心", self.test_service_registry),
            ("服务发现", self.test_service_discovery),
            ("日志服务", self.test_log_service),
            ("对话服务", self.test_chat_service),
            ("服务间通信", self.test_service_communication),
            ("数据库隔离", self.test_database_isolation)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results[test_name] = result
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
                results[test_name] = False
        
        # 测试结果汇总
        print("\n" + "=" * 60)
        print("📋 测试结果汇总:")
        
        passed = 0
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n🎯 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有测试通过！微服务架构运行正常")
            print("\n✨ 架构特点验证:")
            print("  ✅ 服务自动注册和发现")
            print("  ✅ 数据库完全隔离")
            print("  ✅ 服务间API通信")
            print("  ✅ 独立的生命周期管理")
        else:
            print("⚠️ 部分测试失败，请检查服务状态")


async def main():
    """主函数"""
    client = MicroserviceTestClient()
    await client.run_all_tests()


if __name__ == "__main__":
    print("🚀 启动Yuan微服务架构测试")
    print("请确保微服务集群已启动 (python start_all.py)")
    print()
    
    asyncio.run(main())
