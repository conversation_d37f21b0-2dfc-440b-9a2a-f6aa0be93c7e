#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版数据库模型定义
基于用户反馈优化的数据库表结构
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class ConversationImproved(Base):
    """改进版对话记录表"""
    __tablename__ = 'conversations_v2'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 会话管理 - 保留session_id用于逻辑分组
    session_id = Column(String(255), nullable=False, index=True, 
                       comment='会话ID - 用于主题分组、数据分析、用户体验优化')
    session_name = Column(String(500), comment='会话名称 - 用户可自定义的会话标题')
    
    # 消息基本信息
    role = Column(String(50), nullable=False, comment='角色：user/assistant/system')
    content = Column(Text, nullable=False, comment='对话内容')
    message_type = Column(String(20), default='text', 
                         comment='消息类型：text/image/file/audio/video/code')
    
    # 时间管理 - 优化后的时间字段
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True, 
                      comment='对话发生时间 - 核心时间字段')
    created_at = Column(DateTime, default=func.now(), nullable=False, 
                       comment='记录创建时间 - 用于审计和数据完整性')
    # 移除updated_at - 对话记录应该是不可变的
    
    # 附件支持 - 新增功能
    attachments = Column(JSON, comment='''附件信息数组，格式：
    [
        {
            "type": "image",           // 附件类型：image/file/audio/video
            "path": "/uploads/img.jpg", // 文件路径
            "url": "http://...",       // 访问URL（可选）
            "size": 1024,              // 文件大小（字节）
            "name": "screenshot.png",   // 原始文件名
            "mime_type": "image/png",   // MIME类型
            "description": "截图说明"    // 附件描述（可选）
        }
    ]''')
    
    # 元数据 - 扩展的元数据支持
    meta_data = Column(JSON, comment='''扩展元数据，格式：
    {
        "model": "gpt-4",              // 使用的AI模型
        "temperature": 0.7,            // 模型参数
        "tokens": {                    // Token统计
            "input": 150,
            "output": 200,
            "total": 350
        },
        "processing_time": 1.5,        // 处理时间（秒）
        "cost": 0.001,                 // 成本（可选）
        "language": "zh-CN",           // 语言
        "sentiment": "positive",        // 情感分析（可选）
        "topics": ["AI", "数据库"],     // 主题标签（可选）
        "quality_score": 8.5           // 质量评分（可选）
    }''')
    
    # 索引优化
    __table_args__ = (
        # 复合索引：按会话和时间查询
        {'mysql_engine': 'InnoDB', 'mysql_charset': 'utf8mb4'},
    )
    
    def __repr__(self):
        return f"<ConversationV2(id={self.id}, session='{self.session_id}', role='{self.role}', type='{self.message_type}')>"


class SessionInfo(Base):
    """会话信息表 - 新增表，管理会话元数据"""
    __tablename__ = 'session_info'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), nullable=False, unique=True, index=True, 
                       comment='会话ID - 与conversations表关联')
    
    # 会话基本信息
    name = Column(String(500), comment='会话名称')
    description = Column(Text, comment='会话描述')
    status = Column(String(20), default='active', 
                   comment='会话状态：active/archived/deleted')
    
    # 会话统计
    message_count = Column(Integer, default=0, comment='消息总数')
    total_tokens = Column(Integer, default=0, comment='总Token数')
    total_cost = Column(Float, default=0.0, comment='总成本')
    
    # 会话元数据
    participants = Column(JSON, comment='参与者信息')
    tags = Column(JSON, comment='会话标签')
    settings = Column(JSON, comment='会话设置（模型参数等）')
    
    # 时间管理
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='会话创建时间')
    last_activity = Column(DateTime, default=func.now(), onupdate=func.now(), 
                          comment='最后活动时间')
    archived_at = Column(DateTime, comment='归档时间')
    
    def __repr__(self):
        return f"<SessionInfo(session_id='{self.session_id}', name='{self.name}', status='{self.status}')>"


# 改进的数据库核心类建议
class DatabaseCoreImproved:
    """改进版数据库核心类的设计建议"""
    
    async def save_conversation(self, session_id: str, role: str, content: str, 
                               message_type: str = 'text', attachments: list = None,
                               metadata: dict = None, custom_timestamp: datetime = None):
        """
        保存对话记录 - 改进版
        
        Args:
            session_id: 会话ID
            role: 角色
            content: 内容
            message_type: 消息类型
            attachments: 附件列表
            metadata: 元数据
            custom_timestamp: 自定义时间戳（可选，默认使用系统时间）
        
        时间管理策略：
        - 默认使用数据库系统时间（func.now()）确保一致性
        - 支持传入自定义时间戳（用于数据迁移等特殊场景）
        - timestamp字段记录对话发生时间
        - created_at字段记录数据库写入时间
        """
        pass
    
    async def create_session(self, session_id: str = None, name: str = None, 
                           description: str = None, settings: dict = None):
        """
        创建新会话
        
        Args:
            session_id: 会话ID（可选，默认生成UUID）
            name: 会话名称
            description: 会话描述
            settings: 会话设置
        """
        pass
    
    async def get_session_conversations(self, session_id: str, 
                                      include_attachments: bool = True,
                                      limit: int = 100):
        """
        获取会话的完整对话记录
        
        Args:
            session_id: 会话ID
            include_attachments: 是否包含附件信息
            limit: 限制数量
        """
        pass


# 使用示例和最佳实践
"""
## 改进后的使用示例

### 1. 创建会话
```python
session_id = await db.create_session(
    name="Yuan项目讨论",
    description="关于Yuan项目数据库设计的讨论",
    settings={"model": "gpt-4", "temperature": 0.7}
)
```

### 2. 保存文本消息
```python
await db.save_conversation(
    session_id=session_id,
    role="user",
    content="我对数据库表设计有疑问",
    metadata={"language": "zh-CN", "topics": ["数据库", "设计"]}
)
```

### 3. 保存带附件的消息
```python
await db.save_conversation(
    session_id=session_id,
    role="user",
    content="这是数据库设计图",
    message_type="image",
    attachments=[{
        "type": "image",
        "path": "/uploads/db_design.png",
        "size": 2048576,
        "name": "数据库设计图.png",
        "mime_type": "image/png"
    }]
)
```

### 4. 时间管理最佳实践
- 默认情况下，数据库模块自动管理时间
- timestamp使用数据库系统时间确保一致性
- created_at记录实际写入时间用于审计
- 支持传入自定义时间戳用于数据迁移等场景
"""

# 数据库迁移建议
"""
## 数据库迁移策略

1. 创建新表 conversations_v2
2. 保留原表 conversations 确保兼容性
3. 逐步迁移数据
4. 更新应用代码
5. 最终切换到新表结构

## 向后兼容性
- 保持原有API接口不变
- 新功能通过可选参数提供
- 渐进式升级，不影响现有功能
"""
