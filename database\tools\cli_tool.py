#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行数据库管理工具
提供强大的命令行接口，支持脚本化操作和AI调用
"""

import asyncio
import argparse
import json
import sys
import csv
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from tabulate import tabulate

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from database.core import DatabaseCore
from database.models import TABLE_NAMES


class DatabaseCLI:
    """命令行数据库管理器"""
    
    def __init__(self):
        self.db = DatabaseCore()
        self.output_format = 'table'  # table, json, csv
    
    async def initialize_db(self):
        """初始化数据库连接"""
        await self.db.initialize()
    
    async def close_db(self):
        """关闭数据库连接"""
        await self.db.close()
    
    def set_output_format(self, format_type: str):
        """设置输出格式"""
        if format_type in ['table', 'json', 'csv']:
            self.output_format = format_type
        else:
            raise ValueError(f"不支持的输出格式: {format_type}")
    
    def output_data(self, data: List[Dict], headers: Optional[List[str]] = None):
        """根据设置的格式输出数据"""
        if not data:
            print("没有数据")
            return
        
        if self.output_format == 'json':
            print(json.dumps(data, ensure_ascii=False, indent=2, default=str))
        elif self.output_format == 'csv':
            if headers is None:
                headers = list(data[0].keys()) if data else []
            
            writer = csv.DictWriter(sys.stdout, fieldnames=headers)
            writer.writeheader()
            writer.writerows(data)
        else:  # table format
            if headers is None:
                headers = list(data[0].keys()) if data else []
            
            table_data = []
            for row in data:
                table_data.append([str(row.get(h, '')) for h in headers])
            
            print(tabulate(table_data, headers=headers, tablefmt='grid'))
    
    async def show_status(self):
        """显示数据库状态"""
        print("数据库状态信息")
        print("-" * 30)
        
        # 数据库文件信息
        db_path = self.db.db_path
        if db_path.exists():
            size_mb = db_path.stat().st_size / (1024 * 1024)
            print(f"数据库文件: {db_path}")
            print(f"文件大小: {size_mb:.2f} MB")
        else:
            print("数据库文件不存在")
        
        # 表统计信息
        stats = await self.db.get_table_stats()
        
        if self.output_format == 'json':
            result = {
                'database_file': str(db_path),
                'file_size_mb': size_mb if db_path.exists() else 0,
                'table_stats': stats,
                'total_records': sum(stats.values())
            }
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"\n表记录统计:")
            table_data = [[table, count] for table, count in stats.items()]
            print(tabulate(table_data, headers=['表名', '记录数'], tablefmt='grid'))
            print(f"\n总记录数: {sum(stats.values()):,}")
    
    async def query_data(self, table: str = None, sql: str = None, limit: int = 10, 
                        session_id: str = None, level: str = None, hours: int = 24):
        """查询数据"""
        if sql:
            # 执行自定义SQL查询
            if not sql.upper().strip().startswith('SELECT'):
                print("错误: 只支持SELECT查询")
                return
            
            result = await self.db.execute_query(sql)
            self.output_data(result)
            
        elif table:
            # 查询指定表
            if table not in TABLE_NAMES:
                print(f"错误: 无效的表名 {table}，支持的表: {', '.join(TABLE_NAMES)}")
                return
            
            if table == 'conversations':
                if session_id:
                    result = await self.db.get_conversation_history(session_id, limit)
                else:
                    query = f"SELECT * FROM conversations ORDER BY timestamp DESC LIMIT {limit}"
                    result = await self.db.execute_query(query)
                
            elif table == 'system_metrics':
                result = await self.db.get_system_metrics(hours)
                result = result[:limit]  # 限制数量
                
            elif table == 'logs':
                result = await self.db.get_logs(level=level, hours=hours, limit=limit)
                
            else:
                query = f"SELECT * FROM {table} ORDER BY id DESC LIMIT {limit}"
                result = await self.db.execute_query(query)
            
            self.output_data(result)
        else:
            print("错误: 必须指定表名或SQL查询")
    
    async def insert_conversation(self, session_id: str, role: str, content: str, 
                                 metadata: str = None):
        """插入对话记录"""
        try:
            metadata_dict = json.loads(metadata) if metadata else None
            await self.db.save_conversation(session_id, role, content, metadata_dict)
            # 等待批量写入
            await asyncio.sleep(1)
            print(f"成功插入对话记录: {session_id} - {role}")
        except Exception as e:
            print(f"插入失败: {e}")
    
    async def insert_log(self, level: str, module: str, message: str, 
                        function_name: str = None, line_number: int = None,
                        exception_info: str = None, extra_data: str = None):
        """插入日志记录"""
        try:
            extra_dict = json.loads(extra_data) if extra_data else None
            await self.db.save_log(level, module, message, function_name, 
                                 line_number, exception_info, extra_dict)
            await asyncio.sleep(1)
            print(f"成功插入日志记录: {level} - {module}")
        except Exception as e:
            print(f"插入失败: {e}")
    
    async def export_data(self, table: str, filename: str = None, limit: int = 1000):
        """导出数据"""
        if table not in TABLE_NAMES:
            print(f"错误: 无效的表名 {table}")
            return
        
        query = f"SELECT * FROM {table} ORDER BY id DESC LIMIT {limit}"
        result = await self.db.execute_query(query)
        
        if not result:
            print("没有数据可导出")
            return
        
        if filename is None:
            filename = f"{table}_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"数据已导出到: {filename}")
        print(f"导出记录数: {len(result)}")
    
    async def cleanup_data(self, days: int = 30, confirm: bool = False):
        """清理旧数据"""
        if not confirm:
            print(f"将清理 {days} 天前的数据，请使用 --confirm 参数确认操作")
            return
        
        await self.db.cleanup_old_data(days)
        print(f"已清理 {days} 天前的旧数据")
    
    async def maintenance(self, operation: str):
        """数据库维护操作"""
        if operation == 'reindex':
            await self.db.execute_query("REINDEX")
            print("索引重建完成")
        elif operation == 'analyze':
            await self.db.execute_query("ANALYZE")
            print("表统计信息分析完成")
        elif operation == 'integrity':
            result = await self.db.execute_query("PRAGMA integrity_check")
            if result and result[0].get('integrity_check') == 'ok':
                print("数据库完整性检查通过")
            else:
                print("数据库完整性检查发现问题")
                self.output_data(result)
        else:
            print(f"不支持的维护操作: {operation}")
            print("支持的操作: reindex, analyze, integrity")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Yuan项目数据库命令行管理工具')
    
    # 输出格式选项
    parser.add_argument('--format', choices=['table', 'json', 'csv'], 
                       default='table', help='输出格式')
    
    # 主要操作
    parser.add_argument('--status', action='store_true', help='显示数据库状态')
    parser.add_argument('--query', type=str, help='执行SQL查询')
    parser.add_argument('--table', type=str, help='查询指定表')
    parser.add_argument('--export', type=str, help='导出指定表数据')
    parser.add_argument('--cleanup', type=int, metavar='DAYS', help='清理N天前的旧数据')
    parser.add_argument('--maintenance', type=str, choices=['reindex', 'analyze', 'integrity'],
                       help='执行维护操作')
    
    # 查询选项
    parser.add_argument('--limit', type=int, default=10, help='限制查询结果数量')
    parser.add_argument('--session-id', type=str, help='会话ID过滤')
    parser.add_argument('--level', type=str, help='日志级别过滤')
    parser.add_argument('--hours', type=int, default=24, help='时间范围（小时）')
    
    # 插入操作
    parser.add_argument('--insert-conversation', action='store_true', help='插入对话记录')
    parser.add_argument('--role', type=str, help='对话角色')
    parser.add_argument('--content', type=str, help='对话内容')
    parser.add_argument('--metadata', type=str, help='元数据（JSON格式）')
    
    parser.add_argument('--insert-log', action='store_true', help='插入日志记录')
    parser.add_argument('--module', type=str, help='模块名称')
    parser.add_argument('--message', type=str, help='日志消息')
    parser.add_argument('--function', type=str, help='函数名称')
    parser.add_argument('--line', type=int, help='行号')
    parser.add_argument('--exception', type=str, help='异常信息')
    parser.add_argument('--extra', type=str, help='额外数据（JSON格式）')
    
    # 其他选项
    parser.add_argument('--filename', type=str, help='导出文件名')
    parser.add_argument('--confirm', action='store_true', help='确认执行危险操作')
    
    args = parser.parse_args()
    
    # 创建CLI实例
    cli = DatabaseCLI()
    cli.set_output_format(args.format)
    
    try:
        await cli.initialize_db()
        
        # 执行相应操作
        if args.status:
            await cli.show_status()
        elif args.query:
            await cli.query_data(sql=args.query)
        elif args.table:
            await cli.query_data(table=args.table, limit=args.limit, 
                               session_id=args.session_id, level=args.level, hours=args.hours)
        elif args.export:
            await cli.export_data(args.export, args.filename, args.limit)
        elif args.cleanup:
            await cli.cleanup_data(args.cleanup, args.confirm)
        elif args.maintenance:
            await cli.maintenance(args.maintenance)
        elif args.insert_conversation:
            if not all([args.session_id, args.role, args.content]):
                print("错误: 插入对话记录需要 --session-id, --role, --content 参数")
            else:
                await cli.insert_conversation(args.session_id, args.role, args.content, args.metadata)
        elif args.insert_log:
            if not all([args.level, args.module, args.message]):
                print("错误: 插入日志记录需要 --level, --module, --message 参数")
            else:
                await cli.insert_log(args.level, args.module, args.message, 
                                   args.function, args.line, args.exception, args.extra)
        else:
            parser.print_help()
    
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"错误: {e}")
    finally:
        await cli.close_db()


if __name__ == "__main__":
    asyncio.run(main())
