#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话微服务 - 独立数据库版本
只维护对话相关的数据，拥有自己的SQLite数据库
"""

import asyncio
import sys
import uuid
import json
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime
from fastapi import HTTPException
from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.sql import func

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from microservices_v2.base_service import BaseService

# 数据库模型
Base = declarative_base()


class Conversation(Base):
    """对话记录表 - 仅对话相关数据"""
    __tablename__ = 'conversations'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), nullable=False, index=True, comment='会话ID')
    role = Column(String(50), nullable=False, comment='角色：user/assistant/system')
    content = Column(Text, nullable=False, comment='对话内容')
    timestamp = Column(DateTime, default=func.now(), nullable=False, index=True, comment='对话时间')
    attachments = Column(JSON, comment='附件信息')
    meta_data = Column(JSON, comment='元数据：模型参数、token数等')
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')


class ChatSession(Base):
    """会话信息表"""
    __tablename__ = 'chat_sessions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(255), nullable=False, unique=True, index=True, comment='会话ID')
    session_name = Column(String(500), comment='会话名称')
    status = Column(String(20), default='active', comment='会话状态')
    message_count = Column(Integer, default=0, comment='消息数量')
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    last_activity = Column(DateTime, default=func.now(), onupdate=func.now(), comment='最后活动时间')


# 请求/响应模型
class ChatRequest(BaseModel):
    session_id: Optional[str] = None
    message: str
    attachments: Optional[List[Dict]] = None
    model: str = "gpt-4"
    temperature: float = 0.7


class ChatResponse(BaseModel):
    success: bool
    session_id: str
    message: str
    response: str
    metadata: Dict


class ChatService(BaseService):
    """AI对话微服务"""
    
    def __init__(self):
        super().__init__("chat", 8002)
        
        # 独立的数据库 - 只存储对话相关数据
        self.db_path = Path("data/chat_service.db")
        self.db_path.parent.mkdir(exist_ok=True)
        
        # 创建异步数据库引擎
        self.async_engine = create_async_engine(
            f"sqlite+aiosqlite:///{self.db_path}",
            echo=False
        )
        self.async_session_maker = async_sessionmaker(
            self.async_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        self.setup_chat_routes()
    
    async def initialize_database(self):
        """初始化数据库"""
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print(f"✅ 对话服务数据库初始化完成: {self.db_path}")
    
    async def simulate_ai_response(self, message: str, session_id: str, model: str = "gpt-4") -> str:
        """模拟AI响应"""
        responses = [
            f"我理解您说的'{message}'。作为Yuan项目的AI助手，我很乐意为您提供帮助。",
            f"关于'{message}'这个问题，让我为您详细解答...",
            f"您提到的'{message}'很有趣。基于我的理解，我认为...",
            f"感谢您的问题'{message}'。根据我的知识库，我可以告诉您...",
        ]
        
        import random
        response = random.choice(responses)
        await asyncio.sleep(0.5)  # 模拟处理时间
        return response
    
    async def save_conversation(self, session_id: str, role: str, content: str,
                               attachments: Optional[List[Dict]] = None,
                               metadata: Optional[Dict] = None):
        """保存对话记录到本地数据库"""
        async with self.async_session_maker() as session:
            conversation = Conversation(
                session_id=session_id,
                role=role,
                content=content,
                attachments=attachments,
                meta_data=metadata
            )
            session.add(conversation)
            await session.commit()
    
    async def get_conversation_history(self, session_id: str, limit: int = 50) -> List[Dict]:
        """获取对话历史"""
        async with self.async_session_maker() as session:
            from sqlalchemy import select
            
            stmt = select(Conversation).where(
                Conversation.session_id == session_id
            ).order_by(Conversation.timestamp.desc()).limit(limit)
            
            result = await session.execute(stmt)
            conversations = result.scalars().all()
            
            return [
                {
                    "role": conv.role,
                    "content": conv.content,
                    "timestamp": conv.timestamp.isoformat() if conv.timestamp else None,
                    "attachments": conv.attachments,
                    "metadata": conv.meta_data
                }
                for conv in conversations
            ]
    
    async def update_session_info(self, session_id: str):
        """更新会话信息"""
        async with self.async_session_maker() as session:
            from sqlalchemy import select, update
            
            # 检查会话是否存在
            stmt = select(ChatSession).where(ChatSession.session_id == session_id)
            result = await session.execute(stmt)
            chat_session = result.scalar_one_or_none()
            
            if not chat_session:
                # 创建新会话
                chat_session = ChatSession(session_id=session_id)
                session.add(chat_session)
            
            # 更新消息数量
            from sqlalchemy import func as sql_func
            count_stmt = select(sql_func.count(Conversation.id)).where(
                Conversation.session_id == session_id
            )
            count_result = await session.execute(count_stmt)
            message_count = count_result.scalar()
            
            chat_session.message_count = message_count
            chat_session.last_activity = datetime.now()
            
            await session.commit()
    
    def setup_chat_routes(self):
        """设置对话相关路由"""
        
        @self.app.post("/chat", response_model=ChatResponse)
        async def chat(request: ChatRequest):
            """处理对话请求"""
            try:
                # 生成或使用现有会话ID
                session_id = request.session_id or str(uuid.uuid4())
                
                # 保存用户消息
                await self.save_conversation(
                    session_id=session_id,
                    role="user",
                    content=request.message,
                    attachments=request.attachments,
                    metadata={
                        "model": request.model,
                        "temperature": request.temperature,
                        "service": "chat"
                    }
                )
                
                # 获取对话历史（用于上下文）
                history = await self.get_conversation_history(session_id, limit=10)
                
                # 生成AI响应
                ai_response = await self.simulate_ai_response(
                    request.message, session_id, request.model
                )
                
                # 保存AI响应
                await self.save_conversation(
                    session_id=session_id,
                    role="assistant",
                    content=ai_response,
                    metadata={
                        "model": request.model,
                        "context_length": len(history),
                        "response_time": 0.5,
                        "service": "chat"
                    }
                )
                
                # 更新会话信息
                await self.update_session_info(session_id)
                
                # 记录到日志服务（通过服务调用）
                await self.call_service("log", "/logs", "POST", {
                    "level": "INFO",
                    "module": "chat_service",
                    "message": f"处理对话: {session_id}",
                    "metadata": {"session_id": session_id, "message_length": len(request.message)}
                })
                
                return ChatResponse(
                    success=True,
                    session_id=session_id,
                    message=request.message,
                    response=ai_response,
                    metadata={
                        "model": request.model,
                        "context_length": len(history),
                        "session_message_count": len(history) + 2
                    }
                )
                
            except Exception as e:
                # 记录错误到日志服务
                await self.call_service("log", "/logs", "POST", {
                    "level": "ERROR",
                    "module": "chat_service",
                    "message": f"对话处理失败: {str(e)}",
                    "exception_info": str(e)
                })
                raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")
        
        @self.app.get("/sessions/{session_id}/history")
        async def get_session_history(session_id: str, limit: int = 50):
            """获取会话历史"""
            try:
                history = await self.get_conversation_history(session_id, limit)
                return {
                    "success": True,
                    "data": history,
                    "count": len(history),
                    "session_id": session_id
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"获取会话历史失败: {str(e)}")
        
        @self.app.get("/sessions")
        async def get_sessions():
            """获取所有会话"""
            async with self.async_session_maker() as session:
                from sqlalchemy import select
                
                stmt = select(ChatSession).order_by(ChatSession.last_activity.desc())
                result = await session.execute(stmt)
                sessions = result.scalars().all()
                
                return {
                    "success": True,
                    "sessions": [
                        {
                            "session_id": s.session_id,
                            "session_name": s.session_name,
                            "status": s.status,
                            "message_count": s.message_count,
                            "created_at": s.created_at.isoformat() if s.created_at else None,
                            "last_activity": s.last_activity.isoformat() if s.last_activity else None
                        }
                        for s in sessions
                    ],
                    "count": len(sessions)
                }
        
        @self.app.get("/stats")
        async def get_chat_stats():
            """获取对话统计"""
            async with self.async_session_maker() as session:
                from sqlalchemy import select, func as sql_func
                
                # 总对话数
                total_conversations = await session.execute(
                    select(sql_func.count(Conversation.id))
                )
                total_count = total_conversations.scalar()
                
                # 总会话数
                total_sessions = await session.execute(
                    select(sql_func.count(ChatSession.id))
                )
                session_count = total_sessions.scalar()
                
                # 按角色统计
                role_stats = await session.execute(
                    select(Conversation.role, sql_func.count(Conversation.id))
                    .group_by(Conversation.role)
                )
                role_data = {role: count for role, count in role_stats.fetchall()}
                
                return {
                    "total_conversations": total_count,
                    "total_sessions": session_count,
                    "role_distribution": role_data,
                    "database_file": str(self.db_path),
                    "service": "chat"
                }
        
        # 重写启动事件以包含数据库初始化
        @self.app.on_event("startup")
        async def startup_event():
            """服务启动事件"""
            print(f"🚀 {self.service_name} 服务启动中...")
            await self.initialize_database()
            await self.register_to_registry()
            self.start_heartbeat()
            print(f"✅ {self.service_name} 服务启动完成")


def main():
    """主函数"""
    service = ChatService()
    service.run()


if __name__ == "__main__":
    main()
